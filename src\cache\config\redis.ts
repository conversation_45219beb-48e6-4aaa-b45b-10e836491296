/**
 * Redis Configuration for Tap2Go Enterprise Caching
 * Upstash Redis with professional connection management
 */

import { Redis } from '@upstash/redis';

// ===== ENVIRONMENT VALIDATION =====

const requiredEnvVars = {
  UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
  UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,
} as const;

// Validate required environment variables
const missingVars = Object.entries(requiredEnvVars)
  .filter(([, value]) => !value)
  .map(([key]) => key);

if (missingVars.length > 0) {
  throw new Error(`Missing Upstash Redis environment variables: ${missingVars.join(', ')}`);
}

// ===== REDIS CLIENT CONFIGURATION =====

export const redisConfig = {
  url: requiredEnvVars.UPSTASH_REDIS_REST_URL!,
  token: requiredEnvVars.UPSTASH_REDIS_REST_TOKEN!,

  // Connection settings optimized for serverless
  retry: {
    retries: 3,
    retryDelayOnFailure: 100,
  },

  // Enable automatic JSON serialization
  automaticDeserialization: true,
} as const;

// ===== REDIS CLIENT INSTANCES =====

// Primary Redis client for general caching
export const redis = new Redis(redisConfig);

// Specialized clients for different use cases
export const sessionRedis = new Redis(redisConfig);

export const analyticsRedis = new Redis(redisConfig);

// ===== CONNECTION HEALTH CHECK =====

export async function checkRedisConnection(): Promise<boolean> {
  try {
    const testKey = 'health-check';
    const testValue = Date.now().toString();
    
    // Test write
    await redis.set(testKey, testValue, { ex: 10 }); // 10 second expiry
    
    // Test read
    const result = await redis.get(testKey);
    
    // Test delete
    await redis.del(testKey);
    
    return result === testValue;
  } catch (error) {
    console.error('Redis connection health check failed:', error);
    return false;
  }
}

// ===== REDIS OPERATION HELPERS =====

export class RedisOperations {
  private client: Redis;

  constructor(client: Redis = redis) {
    this.client = client;
  }

  // ===== BASIC OPERATIONS =====

  async get<T = any>(key: string): Promise<T | null> {
    try {
      return await this.client.get<T>(key);
    } catch (error) {
      console.error(`Redis GET error for key ${key}:`, error);
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds?: number): Promise<boolean> {
    try {
      if (ttlSeconds) {
        await this.client.set(key, value, { ex: ttlSeconds });
      } else {
        await this.client.set(key, value);
      }
      return true;
    } catch (error) {
      console.error(`Redis SET error for key ${key}:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error(`Redis DEL error for key ${key}:`, error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Redis EXISTS error for key ${key}:`, error);
      return false;
    }
  }

  // ===== ADVANCED OPERATIONS =====

  async mget<T = any>(keys: string[]): Promise<(T | null)[]> {
    try {
      const results = await this.client.mget(...keys);
      return results as (T | null)[];
    } catch (error) {
      console.error(`Redis MGET error for keys ${keys.join(', ')}:`, error);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs: Record<string, any>): Promise<boolean> {
    try {
      const pairsObject: Record<string, any> = {};
      Object.entries(keyValuePairs).forEach(([key, value]) => {
        pairsObject[key] = value;
      });
      await this.client.mset(pairsObject);
      return true;
    } catch (error) {
      console.error('Redis MSET error:', error);
      return false;
    }
  }

  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    try {
      await this.client.expire(key, ttlSeconds);
      return true;
    } catch (error) {
      console.error(`Redis EXPIRE error for key ${key}:`, error);
      return false;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error(`Redis TTL error for key ${key}:`, error);
      return -1;
    }
  }

  // ===== PATTERN OPERATIONS =====

  async deletePattern(pattern: string): Promise<number> {
    try {
      // Note: Upstash Redis doesn't support SCAN, so we use a different approach
      // This is a simplified version - in production, you might want to track keys
      console.warn(`Pattern deletion not fully supported in Upstash Redis: ${pattern}`);
      return 0;
    } catch (error) {
      console.error(`Redis pattern delete error for pattern ${pattern}:`, error);
      return 0;
    }
  }

  // ===== HASH OPERATIONS =====

  async hget<T = any>(key: string, field: string): Promise<T | null> {
    try {
      return await this.client.hget<T>(key, field);
    } catch (error) {
      console.error(`Redis HGET error for key ${key}, field ${field}:`, error);
      return null;
    }
  }

  async hset(key: string, field: string, value: any): Promise<boolean> {
    try {
      await this.client.hset(key, { [field]: value });
      return true;
    } catch (error) {
      console.error(`Redis HSET error for key ${key}, field ${field}:`, error);
      return false;
    }
  }

  async hgetall(key: string): Promise<Record<string, any> | null> {
    try {
      const result = await this.client.hgetall(key);
      return result as Record<string, any> | null;
    } catch (error) {
      console.error(`Redis HGETALL error for key ${key}:`, error);
      return null;
    }
  }

  // ===== LIST OPERATIONS =====

  async lpush(key: string, ...values: any[]): Promise<number> {
    try {
      return await this.client.lpush(key, ...values);
    } catch (error) {
      console.error(`Redis LPUSH error for key ${key}:`, error);
      return 0;
    }
  }

  async lrange<T = any>(key: string, start: number, stop: number): Promise<T[]> {
    try {
      return await this.client.lrange<T>(key, start, stop);
    } catch (error) {
      console.error(`Redis LRANGE error for key ${key}:`, error);
      return [];
    }
  }

  async ltrim(key: string, start: number, stop: number): Promise<boolean> {
    try {
      await this.client.ltrim(key, start, stop);
      return true;
    } catch (error) {
      console.error(`Redis LTRIM error for key ${key}:`, error);
      return false;
    }
  }
}

// ===== DEFAULT REDIS OPERATIONS INSTANCE =====

export const redisOps = new RedisOperations(redis);
export const sessionOps = new RedisOperations(sessionRedis);
export const analyticsOps = new RedisOperations(analyticsRedis);

// ===== REDIS METRICS =====

export interface RedisMetrics {
  hits: number;
  misses: number;
  errors: number;
  operations: number;
  hitRate: number;
}

class RedisMetricsCollector {
  private metrics: RedisMetrics = {
    hits: 0,
    misses: 0,
    errors: 0,
    operations: 0,
    hitRate: 0,
  };

  recordHit(): void {
    this.metrics.hits++;
    this.metrics.operations++;
    this.updateHitRate();
  }

  recordMiss(): void {
    this.metrics.misses++;
    this.metrics.operations++;
    this.updateHitRate();
  }

  recordError(): void {
    this.metrics.errors++;
    this.metrics.operations++;
  }

  private updateHitRate(): void {
    const total = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;
  }

  getMetrics(): RedisMetrics {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      errors: 0,
      operations: 0,
      hitRate: 0,
    };
  }
}

export const redisMetrics = new RedisMetricsCollector();

// ===== EXPORT DEFAULT =====

export default redis;
